from app import db
from datetime import datetime
import enum

class PurchaseOrderStatus(enum.Enum):
    """Statuts des commandes d'achat"""
    PENDING = "pending"                    # En attente
    PARTIAL_RECEIVED = "partial_received"  # Partiellement reçue
    RECEIVED_UNPAID = "received_unpaid"    # Reçue non payée
    RECEIVED = "received"                  # Reçue et payée
    CANCELLED = "cancelled"                # Annulée

    # Note: Le statut "paid" a été supprimé pour éviter la confusion
    # On utilise "received" pour "Reçue et payée"

    def __str__(self):
        return self.value

    @classmethod
    def from_str(cls, value):
        try:
            return cls(value.lower())
        except ValueError:
            return cls.PENDING

class PurchaseOrder(db.Model):
    """Modèle pour les commandes d'achat/approvisionnement"""
    __tablename__ = 'purchase_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    reference = db.Column(db.String(20), unique=True, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('suppliers.id'), nullable=True)  # Optionnel
    status = db.Column(db.Enum(PurchaseOrderStatus), default=PurchaseOrderStatus.PENDING)
    
    # Dates
    order_date = db.Column(db.DateTime, default=datetime.utcnow)
    expected_delivery_date = db.Column(db.DateTime)
    actual_delivery_date = db.Column(db.DateTime)
    
    # Montants
    subtotal = db.Column(db.Float, default=0)
    discount_amount = db.Column(db.Float, default=0)  # Remise globale sur le total
    discount_type = db.Column(db.String(20), default='amount')  # 'amount' ou 'percentage'
    apply_discount_to_items = db.Column(db.Boolean, default=False)  # Appliquer la remise aux prix des articles
    tax_rate = db.Column(db.Float, default=0)
    tax_amount = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float, default=0)
    
    # Informations complémentaires
    notes = db.Column(db.Text)
    delivery_address = db.Column(db.Text)
    
    # Utilisateurs
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)  # Créateur
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    items = db.relationship('PurchaseOrderItem', backref='purchase_order', lazy='dynamic', cascade='all, delete-orphan')
    invoices = db.relationship('SupplierInvoice', backref='purchase_order', lazy='dynamic')
    supplier = db.relationship('Supplier', backref='purchase_orders')
    user = db.relationship('User', foreign_keys=[user_id], backref='created_purchase_orders')
    owner = db.relationship('User', foreign_keys=[owner_id], backref='owned_purchase_orders')
    
    def __repr__(self):
        return f'<PurchaseOrder {self.reference}>'
    
    @property
    def supplier_name(self):
        """Nom du fournisseur ou 'Autres' si non spécifié"""
        return self.supplier.name if self.supplier else 'Autres'
    
    @property
    def is_paid(self):
        """Vérifie si la commande est entièrement payée"""
        return self.status == PurchaseOrderStatus.RECEIVED  # "Reçue et payée"

    @property
    def is_received(self):
        """Vérifie si la marchandise est reçue"""
        return self.status in [PurchaseOrderStatus.RECEIVED, PurchaseOrderStatus.RECEIVED_UNPAID]
    
    def calculate_totals(self):
        """Calcule les totaux de la commande"""
        self.subtotal = sum(item.total_price for item in self.items)

        # Si la remise est appliquée aux prix individuels, ne pas la soustraire une deuxième fois
        if self.apply_discount_to_items:
            # Les prix des articles ont déjà été réduits, donc le subtotal est déjà le total final
            discounted_subtotal = self.subtotal
        else:
            # Appliquer la remise au total
            discounted_subtotal = self.subtotal
            discount_amount = self.discount_amount or 0

            if discount_amount > 0:
                if self.discount_type == 'percentage':
                    discounted_subtotal = self.subtotal * (1 - discount_amount / 100)
                else:  # amount
                    discounted_subtotal = max(0, self.subtotal - discount_amount)

        tax_rate = self.tax_rate or 0
        self.tax_amount = discounted_subtotal * (tax_rate / 100)
        self.total_amount = discounted_subtotal + self.tax_amount

    def apply_discount_to_item_prices(self):
        """Applique la remise globale aux prix des articles individuels"""
        discount_amount = self.discount_amount or 0
        if not self.apply_discount_to_items or discount_amount <= 0:
            return

        # D'abord, sauvegarder les prix originaux si ce n'est pas déjà fait
        for item in self.items:
            if not item.original_unit_price:
                item.original_unit_price = item.unit_price

        if self.discount_type == 'percentage':
            discount_factor = 1 - (discount_amount / 100)
            for item in self.items:
                item.discount_amount = item.original_unit_price * (discount_amount / 100)
                item.unit_price = item.original_unit_price * discount_factor
                item.total_price = item.quantity * item.unit_price
        else:  # amount
            total_original = sum(item.quantity * item.original_unit_price for item in self.items)
            if total_original > 0:
                for item in self.items:
                    item_proportion = (item.quantity * item.original_unit_price) / total_original
                    item_discount = discount_amount * item_proportion
                    item.discount_amount = item_discount / item.quantity if item.quantity > 0 else 0
                    item.unit_price = item.original_unit_price - item.discount_amount
                    item.total_price = item.quantity * item.unit_price
    
    def add_item(self, product=None, ingredient=None, quantity=1, unit_price=0, original_unit_price=None):
        """Ajoute un article à la commande"""
        if not product and not ingredient:
            raise ValueError("Un produit ou un ingrédient doit être spécifié")

        item = PurchaseOrderItem(
            purchase_order=self,
            product=product,
            ingredient=ingredient,
            quantity=quantity,
            unit_price=unit_price,
            original_unit_price=original_unit_price or unit_price,
            total_price=quantity * unit_price
        )
        db.session.add(item)
        self.calculate_totals()
        return item

    def update_average_costs(self):
        """Met à jour les coûts moyens des produits et ingrédients lors de la réception"""
        for item in self.items:
            if item.received_quantity > 0:
                if item.product and not item.product.has_recipe:
                    # Mettre à jour le coût moyen du produit
                    item.product.update_average_cost(item.received_quantity, item.unit_price)
                    # Mettre à jour le stock
                    item.product.stock_quantity += item.received_quantity
                elif item.ingredient:
                    # Mettre à jour le coût moyen de l'ingrédient
                    item.ingredient.update_average_cost(item.received_quantity, item.unit_price)
                    # Mettre à jour le stock
                    item.ingredient.stock_quantity += item.received_quantity

        db.session.commit()
    
    def mark_as_received(self, update_stock=True, mark_as_paid=False):
        """Marque la commande comme reçue et met à jour les stocks

        Args:
            update_stock (bool): Mettre à jour le stock
            mark_as_paid (bool): Si True, marque comme reçu et payé, sinon reçu non payé
        """
        if self.status in [PurchaseOrderStatus.PENDING, PurchaseOrderStatus.PARTIAL_RECEIVED]:
            # Sauvegarder le statut actuel avant de le changer
            current_status = self.status

            # Déterminer le nouveau statut
            if mark_as_paid:
                self.status = PurchaseOrderStatus.RECEIVED  # Reçu et payé
            else:
                self.status = PurchaseOrderStatus.RECEIVED_UNPAID  # Reçu non payé

                # Créer une facture fournisseur si elle n'existe pas déjà et qu'on n'est pas en mode payé
                from app.modules.inventory.models_supplier_invoice import SupplierInvoice, PaymentStatus
                existing_invoice = SupplierInvoice.query.filter_by(
                    purchase_order_id=self.id
                ).first()

                if not existing_invoice and self.supplier_id:
                    invoice = SupplierInvoice(
                        reference=SupplierInvoice.generate_reference(),
                        supplier_id=self.supplier_id,
                        purchase_order_id=self.id,
                        amount=self.total_amount,
                        payment_status=PaymentStatus.PENDING,
                        user_id=self.user_id,
                        owner_id=self.owner_id,
                        description=f"Facture pour commande {self.reference}"
                    )
                    db.session.add(invoice)

            self.actual_delivery_date = datetime.utcnow()

            if update_stock:
                for item in self.items:
                    # Pour les commandes partiellement reçues, ne mettre à jour que la différence
                    if current_status == PurchaseOrderStatus.PARTIAL_RECEIVED:
                        remaining_quantity = item.quantity - (item.received_quantity or 0)
                        if remaining_quantity > 0:
                            item.update_stock(quantity=remaining_quantity)
                            item.received_quantity = item.quantity
                    else:
                        # Pour les commandes en attente, recevoir tout
                        item.update_stock()
                        item.received_quantity = item.quantity

            db.session.commit()
            return True
        return False
    
    def mark_as_paid(self):
        """Marque la commande comme payée (reçue et payée)"""
        if self.status == PurchaseOrderStatus.RECEIVED_UNPAID:
            self.status = PurchaseOrderStatus.RECEIVED  # "Reçue et payée"
            db.session.commit()
            return True
        return False

    def mark_as_received_unpaid(self, update_stock=True):
        """Marque la commande comme reçue mais non payée"""
        if self.status == PurchaseOrderStatus.PENDING:
            self.status = PurchaseOrderStatus.RECEIVED_UNPAID
            self.actual_delivery_date = datetime.utcnow()

            if update_stock:
                for item in self.items:
                    item.update_stock()

            db.session.commit()
            return True
        return False
    
    @staticmethod
    def generate_reference():
        """Génère une référence unique pour la commande"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        count = PurchaseOrder.query.count() + 1
        return f"PO{timestamp}{count:04d}"

class PurchaseOrderItem(db.Model):
    """Articles d'une commande d'achat"""
    __tablename__ = 'purchase_order_items'
    
    id = db.Column(db.Integer, primary_key=True)
    purchase_order_id = db.Column(db.Integer, db.ForeignKey('purchase_orders.id'), nullable=False)
    
    # L'article peut être soit un produit soit un ingrédient
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=True)
    ingredient_id = db.Column(db.Integer, db.ForeignKey('ingredients.id'), nullable=True)

    
    # Quantités et prix
    quantity = db.Column(db.Float, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)  # Prix unitaire modifié
    original_unit_price = db.Column(db.Float)  # Prix unitaire original (référentiel)
    total_price = db.Column(db.Float, nullable=False)
    discount_amount = db.Column(db.Float, default=0)  # Remise appliquée sur cet article

    # Informations complémentaires
    notes = db.Column(db.Text)
    received_quantity = db.Column(db.Float, default=0)  # Quantité effectivement reçue
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    product = db.relationship('Product', backref='purchase_order_items')
    ingredient = db.relationship('Ingredient', backref='purchase_order_items')
    
    def __repr__(self):
        item_name = self.product.name if self.product else self.ingredient.name
        return f'<PurchaseOrderItem {item_name} x{self.quantity}>'
    
    @property
    def item_name(self):
        """Nom de l'article (produit ou ingrédient)"""
        return self.product.name if self.product else self.ingredient.name
    
    @property
    def item_type(self):
        """Type d'article (product ou ingredient)"""
        return 'product' if self.product else 'ingredient'

    @property
    def unit(self):
        """Unité de l'article (produit ou ingrédient)"""
        return self.product.unit if self.product else self.ingredient.unit
    
    @property
    def current_stock(self):
        """Stock actuel de l'article"""
        if self.product:
            return self.product.stock_quantity
        elif self.ingredient:
            return self.ingredient.stock_quantity
        return 0
    
    def update_stock(self, quantity=None):
        """Met à jour le stock de l'article"""
        if quantity is None:
            quantity = self.quantity

        if self.product:
            success = self.product.update_stock(
                quantity=quantity,
                operation='add',
                reason='PURCHASE',
                reference=f"PO #{self.purchase_order.reference}",
                notes=f"Approvisionnement via commande {self.purchase_order.reference}"
            )
        elif self.ingredient:
            success = self.ingredient.update_stock(
                quantity=quantity,
                operation='add',
                reason='PURCHASE',
                reference=f"PO #{self.purchase_order.reference}",
                notes=f"Approvisionnement via commande {self.purchase_order.reference}"
            )
        else:
            success = False

        # NE PAS modifier received_quantity ici car c'est géré par la logique de réception partielle
        # Cette méthode ne fait que mettre à jour le stock physique
        if success:
            db.session.commit()

        return success
