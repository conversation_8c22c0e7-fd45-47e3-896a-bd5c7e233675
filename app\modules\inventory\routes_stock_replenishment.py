from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app.extensions import db
from app.utils.decorators import inventory_access_required as permission_required
from app.modules.inventory.models_purchase_order import PurchaseOrder, PurchaseOrderItem, PurchaseOrderStatus
from app.modules.inventory.models_supplier_invoice import SupplierInvoice, SupplierPayment, PaymentStatus, SupplierPaymentMethod
from app.modules.inventory.models_supplier import Supplier
from app.modules.inventory.models_supplier_category import SupplierCategory
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.inventory.models_ingredient import Ingredient, IngredientCategory
from app.modules.inventory.models_bank_account import BankAccount
from app.modules.inventory.forms_stock_replenishment import (
    StockReplenishmentModeForm, StockReplenishmentFormMode, PurchaseOrderPaymentForm,
    POSStockReplenishmentForm, QuickStockAdjustmentForm, BulkSupplierPaymentForm,
    populate_form_choices
)
from app.modules.inventory.services_cost_management import CostManagementService
from app.modules.inventory.services_purchase_management import PurchaseManagementService
from sqlalchemy import or_
import json
from datetime import datetime
from . import bp

@bp.route('/stock-replenishment')
@login_required
@permission_required
def stock_replenishment_index():
    """Page principale d'approvisionnement avec sélecteur de mode"""
    form = StockReplenishmentModeForm()
    
    # Statistiques rapides
    pending_orders = PurchaseOrder.query.filter_by(
        owner_id=current_user.id,
        status=PurchaseOrderStatus.PENDING
    ).count()
    
    unpaid_invoices = SupplierInvoice.query.filter_by(
        owner_id=current_user.id,
        payment_status=PaymentStatus.PENDING
    ).count()
    
    low_stock_products = Product.query.filter(
        Product.owner_id == current_user.id,
        Product.has_recipe == False,
        Product.stock_quantity <= Product.minimum_stock
    ).count()
    
    low_stock_ingredients = Ingredient.query.filter(
        Ingredient.owner_id == current_user.id,
        Ingredient.stock_quantity <= Ingredient.minimum_stock
    ).count()
    
    stats = {
        'pending_orders': pending_orders,
        'unpaid_invoices': unpaid_invoices,
        'low_stock_items': low_stock_products + low_stock_ingredients
    }
    
    return render_template('inventory/stock_replenishment/index.html', 
                         form=form, stats=stats)

@bp.route('/stock-replenishment/form-mode')
@login_required
@permission_required
def stock_replenishment_form_mode():
    """Mode formulaire pour l'approvisionnement"""
    form = StockReplenishmentFormMode()
    
    # Peupler les choix du formulaire
    populate_choices = populate_form_choices()
    populate_choices['populate_supplier_choices'](form)
    populate_choices['populate_product_choices'](form)
    populate_choices['populate_ingredient_choices'](form)
    
    return render_template('inventory/stock_replenishment/form_mode.html', form=form, now=datetime.utcnow())

@bp.route('/stock-replenishment/pos-mode')
@login_required
@permission_required
def stock_replenishment_pos_mode():
    """Mode POS pour l'approvisionnement"""
    # Récupérer les données nécessaires pour l'interface POS
    
    # Catégories de fournisseurs
    supplier_categories = SupplierCategory.query.filter_by(
        owner_id=current_user.id,
        is_active=True
    ).all()
    
    # Fournisseurs
    suppliers = Supplier.query.filter_by(
        owner_id=current_user.id,
        is_active=True
    ).all()
    
    # Catégories de produits sans recettes
    product_categories = ProductCategory.query.join(Product).filter(
        ProductCategory.owner_id == current_user.id,
        Product.has_recipe == False,
        Product.is_active == True
    ).distinct().all()
    
    # Catégories d'ingrédients
    ingredient_categories = IngredientCategory.query.filter_by(
        owner_id=current_user.id
    ).all()
    
    # Produits sans recettes
    products = Product.query.filter_by(
        owner_id=current_user.id,
        has_recipe=False,
        is_active=True
    ).all()
    
    # Ingrédients
    ingredients = Ingredient.query.filter_by(
        owner_id=current_user.id
    ).all()
    
    # Convertir les objets en dictionnaires pour JSON
    import json

    products_json = json.dumps([{
        'id': p.id,
        'name': p.name,
        'price': p.cost_price or p.price,
        'stock_quantity': p.stock_quantity,
        'unit': p.unit,
        'image_path': url_for('static', filename=p.image_path) if p.image_path else url_for('static', filename='images/default-product.svg'),
        'category_id': p.category_id,
        'type': 'product'
    } for p in products])

    ingredients_json = json.dumps([{
        'id': i.id,
        'name': i.name,
        'price': i.price_per_unit,
        'stock_quantity': i.stock_quantity,
        'unit': i.unit,
        'image_path': url_for('static', filename=i.image_path) if i.image_path else url_for('static', filename='images/default-ingredient.svg'),
        'category_id': i.category_id,
        'type': 'ingredient'
    } for i in ingredients])

    suppliers_json = json.dumps([{
        'id': s.id,
        'name': s.name,
        'contact_name': s.contact_name,
        'phone': s.phone,
        'category_id': s.category_id
    } for s in suppliers])

    product_categories_json = json.dumps([{
        'id': c.id,
        'name': c.name
    } for c in product_categories])

    ingredient_categories_json = json.dumps([{
        'id': c.id,
        'name': c.name
    } for c in ingredient_categories])

    return render_template('inventory/stock_replenishment/pos_mode.html',
                         supplier_categories=supplier_categories,
                         suppliers=suppliers,
                         product_categories=product_categories,
                         ingredient_categories=ingredient_categories,
                         products=products,
                         ingredients=ingredients,
                         products_json=products_json,
                         ingredients_json=ingredients_json,
                         suppliers_json=suppliers_json,
                         product_categories_json=product_categories_json,
                         ingredient_categories_json=ingredient_categories_json,
                         now=datetime.utcnow())

@bp.route('/stock-replenishment/api/suppliers/<int:category_id>')
@login_required
@permission_required
def get_suppliers_by_category(category_id):
    """API pour récupérer les fournisseurs par catégorie"""
    if category_id == 0:
        suppliers = Supplier.query.filter_by(
            owner_id=current_user.id,
            is_active=True
        ).all()
    else:
        suppliers = Supplier.query.filter_by(
            owner_id=current_user.id,
            category_id=category_id,
            is_active=True
        ).all()
    
    return jsonify([{
        'id': supplier.id,
        'name': supplier.name,
        'contact_name': supplier.contact_name,
        'phone': supplier.phone
    } for supplier in suppliers])

@bp.route('/stock-replenishment/api/items/<item_type>')
@login_required
@permission_required
def get_items_by_type(item_type):
    """API pour récupérer les articles par type"""
    if item_type == 'products':
        items = Product.query.filter_by(
            owner_id=current_user.id,
            has_recipe=False,
            is_active=True
        ).all()
        
        return jsonify([{
            'id': item.id,
            'name': item.name,
            'price': item.cost_price or item.price,
            'stock_quantity': item.stock_quantity,
            'unit': item.unit,
            'image_path': item.image_path,
            'category_id': item.category_id,
            'type': 'product'
        } for item in items])
    
    elif item_type == 'ingredients':
        items = Ingredient.query.filter_by(
            owner_id=current_user.id
        ).all()
        
        return jsonify([{
            'id': item.id,
            'name': item.name,
            'price': item.price_per_unit,
            'stock_quantity': item.stock_quantity,
            'unit': item.unit,
            'image_path': item.image_path,
            'category_id': item.category_id,
            'type': 'ingredient'
        } for item in items])
    
    return jsonify([])

@bp.route('/stock-replenishment/api/items/search')
@login_required
@permission_required
def search_items():
    """API pour rechercher des articles"""
    query = request.args.get('q', '').strip()
    if not query:
        return jsonify([])
    
    # Rechercher dans les produits sans recettes
    products = Product.query.filter(
        Product.owner_id == current_user.id,
        Product.has_recipe == False,
        Product.is_active == True,
        Product.name.ilike(f'%{query}%')
    ).limit(10).all()
    
    # Rechercher dans les ingrédients
    ingredients = Ingredient.query.filter(
        Ingredient.owner_id == current_user.id,
        Ingredient.name.ilike(f'%{query}%')
    ).limit(10).all()
    
    results = []
    
    for product in products:
        results.append({
            'id': product.id,
            'name': product.name,
            'price': product.cost_price or product.price,
            'stock_quantity': product.stock_quantity,
            'unit': product.unit,
            'image_path': product.image_path,
            'category_id': product.category_id,
            'type': 'product'
        })
    
    for ingredient in ingredients:
        results.append({
            'id': ingredient.id,
            'name': ingredient.name,
            'price': ingredient.price_per_unit,
            'stock_quantity': ingredient.stock_quantity,
            'unit': ingredient.unit,
            'image_path': ingredient.image_path,
            'category_id': ingredient.category_id,
            'type': 'ingredient'
        })
    
    return jsonify(results)

@bp.route('/stock-replenishment/process-order', methods=['POST'])
@login_required
@permission_required
def process_stock_replenishment_order():
    """Traite une commande d'approvisionnement (mode POS ou formulaire)"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': 'Aucune donnée reçue'}), 400

        # Validation des données requises
        cart_items = data.get('cart_items', [])
        if not cart_items:
            return jsonify({'success': False, 'error': 'Aucun article dans le panier'}), 400

        supplier_id = data.get('supplier_id')
        if not supplier_id or supplier_id == 0:
            return jsonify({'success': False, 'error': 'Veuillez sélectionner un fournisseur'}), 400

        # Récupérer les informations de remise
        discount_info = data.get('discount', {})

        # Créer la commande d'achat
        purchase_order = PurchaseOrder(
            reference=PurchaseOrder.generate_reference(),
            supplier_id=data.get('supplier_id') if data.get('supplier_id') != 0 else None,
            user_id=current_user.id,
            owner_id=current_user.id,
            notes=data.get('notes', ''),
            subtotal=data.get('subtotal', 0),
            discount_amount=discount_info.get('amount', 0),
            discount_type=discount_info.get('type', 'amount'),
            apply_discount_to_items=discount_info.get('apply_to_items', False),
            total_amount=data.get('total_amount', 0)
        )
        
        # Définir le statut selon le type de traitement
        processing_type = data.get('processing_type', 'receive_and_pay')
        if processing_type == 'order_only':
            purchase_order.status = PurchaseOrderStatus.PENDING
        elif processing_type == 'receive_only':
            purchase_order.status = PurchaseOrderStatus.RECEIVED_UNPAID
            purchase_order.actual_delivery_date = datetime.utcnow()
        elif processing_type == 'receive_pay_later':
            purchase_order.status = PurchaseOrderStatus.RECEIVED_UNPAID
            purchase_order.actual_delivery_date = datetime.utcnow()
        else:  # receive_and_pay
            purchase_order.status = PurchaseOrderStatus.RECEIVED
            purchase_order.actual_delivery_date = datetime.utcnow()
        
        db.session.add(purchase_order)
        db.session.flush()  # Pour obtenir l'ID
        
        # Ajouter les articles
        cart_items = data.get('cart_items', [])
        items_to_update_stock = []

        for item_data in cart_items:
            item = PurchaseOrderItem(
                purchase_order_id=purchase_order.id,
                product_id=item_data.get('product_id') if item_data.get('type') == 'product' else None,
                ingredient_id=item_data.get('ingredient_id') if item_data.get('type') == 'ingredient' else None,
                quantity=item_data.get('quantity'),
                unit_price=item_data.get('unit_price'),
                original_unit_price=item_data.get('original_unit_price'),
                total_price=item_data.get('total_price'),
                notes=item_data.get('price_change_reason')
            )

            # Calculer et sauvegarder le discount_amount si applicable
            if item.original_unit_price and item.unit_price and item.original_unit_price != item.unit_price:
                item.discount_amount = item.original_unit_price - item.unit_price
            else:
                item.discount_amount = 0

            db.session.add(item)

            # Marquer pour mise à jour du stock si la marchandise est reçue
            if processing_type in ['receive_and_pay', 'receive_pay_later']:
                items_to_update_stock.append(item)

        # Flush pour obtenir les IDs des items
        db.session.flush()

        # Appliquer la remise aux prix individuels si demandé
        if discount_info.get('apply_to_items', False) and discount_info.get('amount', 0) > 0:
            # Les prix ont déjà été modifiés côté client, donc on met juste à jour les flags
            # et on recalcule les totaux
            purchase_order.apply_discount_to_items = True

            # Recalculer le discount_amount total basé sur les articles
            total_discount = 0
            for item in purchase_order.items:
                if item.original_unit_price and item.unit_price:
                    item_discount = (item.original_unit_price - item.unit_price) * item.quantity
                    total_discount += item_discount

            purchase_order.discount_amount = total_discount

            # Déterminer le type de remise basé sur les données reçues
            if discount_info.get('type') == 'percentage':
                purchase_order.discount_type = 'percentage'
            else:
                purchase_order.discount_type = 'amount'

            # Recalculer les totaux avec la nouvelle logique
            purchase_order.calculate_totals()

        # Maintenant mettre à jour le stock et les coûts moyens
        for item in items_to_update_stock:
            # Marquer la quantité comme reçue
            item.received_quantity = item.quantity

            # Mettre à jour le stock et le coût moyen
            if item.product and not item.product.has_recipe:
                item.product.update_average_cost(item.quantity, item.unit_price)
                item.product.stock_quantity += item.quantity
            elif item.ingredient:
                item.ingredient.update_average_cost(item.quantity, item.unit_price)
                item.ingredient.stock_quantity += item.quantity

        # Recalculer les totaux
        purchase_order.calculate_totals()
        
        # Créer la facture si nécessaire
        if processing_type in ['receive_and_pay', 'receive_pay_later']:
            invoice = SupplierInvoice(
                reference=SupplierInvoice.generate_reference(),
                supplier_id=purchase_order.supplier_id,
                purchase_order_id=purchase_order.id,
                amount=purchase_order.total_amount,
                payment_status=PaymentStatus.PENDING,
                user_id=current_user.id,
                owner_id=current_user.id
            )
            db.session.add(invoice)
            db.session.flush()  # Pour obtenir l'ID de la facture

            # Traiter le paiement immédiatement si demandé
            if processing_type == 'receive_and_pay':
                payment_method_str = data.get('payment_method')
                if payment_method_str:
                    try:
                        payment_method = SupplierPaymentMethod.from_str(payment_method_str)

                        # Créer le paiement
                        success, message = invoice.add_payment(
                            amount=invoice.amount,
                            method=payment_method,
                            notes=f"Paiement automatique depuis mode POS",
                            user_id=current_user.id
                        )

                        if success:
                            # Traiter l'opération selon la méthode de paiement
                            payment = invoice.payments.order_by(SupplierPayment.id.desc()).first()

                            if payment_method == SupplierPaymentMethod.CASH_CAISSE:
                                success, message = payment.process_cash_register_operation()
                                if not success:
                                    db.session.rollback()
                                    return jsonify({'success': False, 'error': f'Erreur paiement caisse: {message}'})

                            # Marquer la commande comme payée
                            purchase_order.status = PurchaseOrderStatus.PAID
                        else:
                            current_app.logger.warning(f"Échec du paiement automatique: {message}")
                    except Exception as e:
                        current_app.logger.error(f"Erreur lors du paiement automatique: {str(e)}")

        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'Commande traitée avec succès',
            'purchase_order_id': purchase_order.id,
            'processing_type': processing_type
        })
        
    except Exception as e:
        db.session.rollback()
        import traceback
        error_details = traceback.format_exc()
        current_app.logger.error(f"Erreur lors du traitement de la commande: {str(e)}")
        current_app.logger.error(f"Détails de l'erreur: {error_details}")
        return jsonify({
            'success': False,
            'error': f'Erreur lors du traitement de la commande: {str(e)}'
        }), 500



@bp.route('/stock-replenishment/orders/<int:order_id>')
@login_required
@permission_required
def purchase_order_details(order_id):
    """Détails d'une commande d'achat"""
    order = PurchaseOrder.query.filter_by(
        id=order_id,
        owner_id=current_user.id
    ).first_or_404()
    
    from datetime import datetime
    return render_template('inventory/stock_replenishment/order_details.html',
                         order=order,
                         now=datetime.now())

@bp.route('/stock-replenishment/orders/<int:order_id>/receive', methods=['POST'])
@login_required
@permission_required
def receive_purchase_order(order_id):
    """Marquer une commande comme reçue"""
    order = PurchaseOrder.query.filter_by(
        id=order_id,
        owner_id=current_user.id
    ).first_or_404()

    # Vérifier si c'est une requête AJAX (en regardant l'en-tête Accept)
    is_ajax = request.headers.get('Accept', '').find('application/json') != -1

    try:
        if order.mark_as_received():
            message = 'Commande marquée comme reçue et stock mis à jour'
            if is_ajax:
                return jsonify({'success': True, 'message': message})
            else:
                flash(message, 'success')
        else:
            error_msg = 'Impossible de marquer cette commande comme reçue'
            if is_ajax:
                return jsonify({'success': False, 'error': error_msg})
            else:
                flash(error_msg, 'error')
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la réception: {str(e)}")
        error_msg = 'Erreur lors de la réception de la commande'
        if is_ajax:
            return jsonify({'success': False, 'error': error_msg})
        else:
            flash(error_msg, 'error')

    # Si ce n'est pas AJAX, rediriger normalement
    return redirect(url_for('inventory.purchase_order_details', order_id=order_id))

@bp.route('/stock-replenishment/quick-adjustment', methods=['GET', 'POST'])
@login_required
@permission_required
def quick_stock_adjustment():
    """Ajustement rapide de stock"""
    form = QuickStockAdjustmentForm()

    # Peupler les choix de produits et ingrédients
    form.product_id.choices = [(0, 'Sélectionner un produit')] + [
        (p.id, f"{p.name} (Stock: {p.stock_quantity} {p.unit or ''})")
        for p in Product.query.filter_by(
            owner_id=current_user.id,
            has_recipe=False
        ).all()
    ]

    form.ingredient_id.choices = [(0, 'Sélectionner un ingrédient')] + [
        (i.id, f"{i.name} (Stock: {i.stock_quantity} {i.unit or ''})")
        for i in Ingredient.query.filter_by(
            owner_id=current_user.id
        ).all()
    ]

    if form.validate_on_submit():
        try:
            # Déterminer quel type d'article
            if form.product_id.data and form.product_id.data != 0:
                item = Product.query.filter_by(
                    id=form.product_id.data,
                    owner_id=current_user.id
                ).first_or_404()
                item_type = 'product'
            elif form.ingredient_id.data and form.ingredient_id.data != 0:
                item = Ingredient.query.filter_by(
                    id=form.ingredient_id.data,
                    owner_id=current_user.id
                ).first_or_404()
                item_type = 'ingredient'
            else:
                flash('Veuillez sélectionner un produit ou un ingrédient', 'error')
                return render_template('inventory/stock_replenishment/quick_adjustment.html', form=form)

            # Appliquer l'ajustement
            old_stock = item.stock_quantity

            if form.adjustment_type.data == 'increase':
                item.stock_quantity += form.quantity.data
            elif form.adjustment_type.data == 'decrease':
                item.stock_quantity = max(0, item.stock_quantity - form.quantity.data)
            elif form.adjustment_type.data == 'set':
                item.stock_quantity = form.quantity.data

            db.session.commit()

            flash(f'Ajustement de stock effectué avec succès pour {item.name}', 'success')
            return redirect(url_for('inventory.stock_replenishment_index'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Erreur lors de l'ajustement: {str(e)}")
            flash('Erreur lors de l\'ajustement de stock', 'error')

    return render_template('inventory/stock_replenishment/quick_adjustment.html', form=form)

# API Routes pour les paiements fournisseurs

@bp.route('/api/supplier-invoice/<int:invoice_id>')
@login_required
@permission_required
def get_supplier_invoice(invoice_id):
    """API pour récupérer les détails d'une facture fournisseur"""
    invoice = SupplierInvoice.query.filter_by(
        id=invoice_id,
        owner_id=current_user.id
    ).first_or_404()

    return jsonify({
        'id': invoice.id,
        'reference': invoice.reference,
        'amount': invoice.amount,
        'paid_amount': invoice.paid_amount,
        'remaining_amount': invoice.remaining_amount,
        'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
        'is_overdue': invoice.is_overdue,
        'days_overdue': invoice.days_overdue
    })

@bp.route('/api/supplier-invoices/pending/<int:supplier_id>')
@login_required
@permission_required
def get_pending_supplier_invoices(supplier_id):
    """API pour récupérer toutes les factures en attente d'un fournisseur"""
    invoices = SupplierInvoice.query.filter_by(
        supplier_id=supplier_id,
        owner_id=current_user.id,
        payment_status=PaymentStatus.PENDING
    ).order_by(SupplierInvoice.due_date.asc()).all()

    return jsonify([{
        'id': invoice.id,
        'reference': invoice.reference,
        'amount': invoice.amount,
        'remaining_amount': invoice.remaining_amount,
        'due_date': invoice.due_date.isoformat() if invoice.due_date else None,
        'is_overdue': invoice.is_overdue,
        'days_overdue': invoice.days_overdue
    } for invoice in invoices])

@bp.route('/api/bank-accounts')
@login_required
@permission_required
def get_bank_accounts():
    """API pour récupérer les comptes bancaires de l'utilisateur"""
    accounts = BankAccount.query.filter_by(
        owner_id=current_user.id,
        is_active=True
    ).all()

    return jsonify([{
        'id': account.id,
        'name': account.name,
        'balance': account.balance
    } for account in accounts])

@bp.route('/api/pay-supplier-invoice', methods=['POST'])
@login_required
@permission_required
def pay_supplier_invoice():
    """API pour effectuer le paiement d'une facture fournisseur"""
    try:
        data = request.get_json()

        invoice = SupplierInvoice.query.filter_by(
            id=data.get('invoice_id'),
            owner_id=current_user.id
        ).first_or_404()

        if invoice.payment_status == PaymentStatus.PAID:
            return jsonify({'success': False, 'error': 'Facture déjà payée'})

        payment_method = SupplierPaymentMethod.from_str(data.get('payment_method'))
        amount = invoice.remaining_amount

        # Créer le paiement
        success, message = invoice.add_payment(
            amount=amount,
            method=payment_method,
            notes=data.get('notes'),
            user_id=current_user.id
        )

        if not success:
            return jsonify({'success': False, 'error': message})

        # Traiter l'opération selon la méthode de paiement
        payment = invoice.payments.order_by(SupplierPayment.id.desc()).first()

        if payment_method == SupplierPaymentMethod.CASH_CAISSE:
            success, message = payment.process_cash_register_operation()
            if not success:
                db.session.rollback()
                return jsonify({'success': False, 'error': message})

        elif payment_method in [
            SupplierPaymentMethod.CHEQUE_COMPTE_BANQUE,
            SupplierPaymentMethod.VIREMENT_COMPTE_BANQUE,
            SupplierPaymentMethod.SORTIE_CASH_BANQUE
        ]:
            bank_account_id = data.get('bank_account_id')
            if not bank_account_id:
                db.session.rollback()
                return jsonify({'success': False, 'error': 'Compte bancaire requis'})

            payment.bank_account_id = bank_account_id
            payment.reference = data.get('reference')

            success, message = payment.process_bank_operation()
            if not success:
                db.session.rollback()
                return jsonify({'success': False, 'error': message})

        db.session.commit()
        return jsonify({'success': True, 'message': 'Paiement effectué avec succès'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors du paiement: {str(e)}")
        return jsonify({'success': False, 'error': 'Erreur lors du paiement'}), 500

@bp.route('/stock-replenishment/pending-orders')
@login_required
@permission_required
def pending_orders():
    """Page des commandes en attente (Pending Marchandise)"""
    # Récupérer toutes les commandes en attente
    pending_orders = PurchaseOrder.query.filter(
        PurchaseOrder.owner_id == current_user.id,
        PurchaseOrder.status.in_([PurchaseOrderStatus.PENDING, PurchaseOrderStatus.PARTIAL_RECEIVED])
    ).order_by(PurchaseOrder.expected_delivery_date.asc().nullslast(),
               PurchaseOrder.created_at.desc()).all()

    # Calculer les statistiques
    total_pending_amount = sum(order.total_amount for order in pending_orders)
    total_pending_items = sum(order.items.count() for order in pending_orders)
    unique_suppliers = len(set(order.supplier_id for order in pending_orders if order.supplier_id))

    # Récupérer la liste des fournisseurs pour les filtres
    suppliers = Supplier.query.filter_by(
        owner_id=current_user.id,
        is_active=True
    ).all()

    return render_template('inventory/stock_replenishment/pending_orders.html',
                         pending_orders=pending_orders,
                         total_pending_amount=total_pending_amount,
                         total_pending_items=total_pending_items,
                         unique_suppliers=unique_suppliers,
                         suppliers=suppliers,
                         now=datetime.utcnow())

@bp.route('/api/purchase-order/<int:order_id>/items')
@login_required
@permission_required
def get_purchase_order_items(order_id):
    """API pour récupérer les articles d'une commande d'achat"""
    order = PurchaseOrder.query.filter_by(
        id=order_id,
        owner_id=current_user.id
    ).first_or_404()

    items_data = []
    for item in order.items:
        items_data.append({
            'id': item.id,
            'name': item.item_name,
            'quantity': item.quantity,
            'received_quantity': item.received_quantity,
            'unit_price': item.unit_price,
            'total_price': item.total_price,
            'notes': item.notes,
            'unit': getattr(item.product, 'unit', None) if item.product else getattr(item.ingredient, 'unit', None)
        })

    return jsonify({
        'id': order.id,
        'reference': order.reference,
        'items': items_data
    })

@bp.route('/stock-replenishment/orders/<int:order_id>/partial-receive', methods=['POST'])
@login_required
@permission_required
def partial_receive_order(order_id):
    """Traiter une réception partielle de commande"""
    try:
        data = request.get_json()
        quantities = data.get('quantities', {})
        update_stock = data.get('update_stock', True)

        order = PurchaseOrder.query.filter_by(
            id=order_id,
            owner_id=current_user.id
        ).first_or_404()

        if order.status not in [PurchaseOrderStatus.PENDING, PurchaseOrderStatus.PARTIAL_RECEIVED]:
            return jsonify({'success': False, 'error': 'Cette commande ne peut pas être modifiée'})

        # Mettre à jour les quantités reçues
        items_updated = 0
        for item in order.items:
            item_id_str = str(item.id)
            if item_id_str in quantities:
                receive_quantity = float(quantities[item_id_str])
                if receive_quantity > 0:
                    # Vérifier que la quantité ne dépasse pas ce qui reste à recevoir
                    current_received = item.received_quantity or 0
                    remaining = item.quantity - current_received

                    if receive_quantity > remaining:
                        receive_quantity = remaining

                    # Accumuler la quantité reçue
                    if item.received_quantity is None:
                        item.received_quantity = receive_quantity
                    else:
                        item.received_quantity += receive_quantity

                    items_updated += 1

                    # Mettre à jour le stock si demandé
                    if update_stock:
                        item.update_stock(quantity=receive_quantity)

        if items_updated == 0:
            return jsonify({'success': False, 'error': 'Aucune quantité à recevoir'})

        # Mettre à jour le statut de la commande
        all_received = all((item.received_quantity or 0) >= item.quantity for item in order.items)
        if all_received:
            order.status = PurchaseOrderStatus.RECEIVED
            order.actual_delivery_date = datetime.utcnow()
        else:
            order.status = PurchaseOrderStatus.PARTIAL_RECEIVED

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'{items_updated} article(s) mis à jour',
            'new_status': order.status.value
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors de la réception partielle: {str(e)}")
        return jsonify({'success': False, 'error': 'Erreur lors de la réception'}), 500

@bp.route('/stock-replenishment/orders/<int:order_id>/cancel', methods=['POST'])
@login_required
@permission_required
def cancel_order(order_id):
    """Annuler une commande d'achat"""
    try:
        order = PurchaseOrder.query.filter_by(
            id=order_id,
            owner_id=current_user.id
        ).first_or_404()

        if order.status != PurchaseOrderStatus.PENDING:
            return jsonify({'success': False, 'error': 'Seules les commandes en attente peuvent être annulées'})

        order.status = PurchaseOrderStatus.CANCELLED
        db.session.commit()

        return jsonify({'success': True, 'message': 'Commande annulée avec succès'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors de l'annulation: {str(e)}")
        return jsonify({'success': False, 'error': 'Erreur lors de l\'annulation'}), 500



@bp.route('/stock-replenishment/orders')
@login_required
@permission_required
def purchase_orders_list():
    """Liste de toutes les commandes d'achat"""
    page = request.args.get('page', 1, type=int)
    supplier_id = request.args.get('supplier_id', type=int)
    status = request.args.get('status')
    order_date = request.args.get('order_date')

    # Construire la requête de base
    query = PurchaseOrder.query.filter_by(owner_id=current_user.id)

    # Appliquer les filtres
    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)

    if status:
        if status == 'pending':
            query = query.filter_by(status=PurchaseOrderStatus.PENDING)
        elif status == 'partial_received':
            query = query.filter_by(status=PurchaseOrderStatus.PARTIAL_RECEIVED)
        elif status == 'received':
            query = query.filter_by(status=PurchaseOrderStatus.RECEIVED)
        elif status == 'received_unpaid':
            query = query.filter_by(status=PurchaseOrderStatus.RECEIVED_UNPAID)
        elif status == 'paid':
            query = query.filter_by(status=PurchaseOrderStatus.PAID)
        elif status == 'cancelled':
            query = query.filter_by(status=PurchaseOrderStatus.CANCELLED)

    if order_date:
        try:
            date_obj = datetime.strptime(order_date, '%Y-%m-%d').date()
            query = query.filter(db.func.date(PurchaseOrder.order_date) == date_obj)
        except ValueError:
            pass

    # Ordonner par date de création (plus récent en premier)
    query = query.order_by(PurchaseOrder.created_at.desc())

    # Paginer
    orders = query.paginate(
        page=page, per_page=20, error_out=False
    )

    # Récupérer les fournisseurs pour les filtres
    suppliers = Supplier.query.filter_by(
        owner_id=current_user.id,
        is_active=True
    ).all()

    return render_template('inventory/stock_replenishment/orders_list.html',
                         orders=orders,
                         suppliers=suppliers,
                         now=datetime.utcnow())

@bp.route('/stock-replenishment/orders/<int:order_id>/receive', methods=['POST'])
@login_required
@permission_required
def receive_order(order_id):
    """Marquer une commande comme entièrement reçue"""
    try:
        order = PurchaseOrder.query.filter_by(
            id=order_id,
            owner_id=current_user.id
        ).first_or_404()

        success = order.mark_as_received()

        if success:
            db.session.commit()
            return jsonify({'success': True, 'message': 'Commande marquée comme reçue'})
        else:
            return jsonify({'success': False, 'error': 'Impossible de marquer cette commande comme reçue'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors de la réception: {str(e)}")
        return jsonify({'success': False, 'error': 'Erreur lors de la réception'}), 500

@bp.route('/stock-replenishment/orders/<int:order_id>/edit', methods=['GET', 'POST'])
@login_required
@permission_required
def edit_purchase_order(order_id):
    """Modifier une commande d'achat"""
    order = PurchaseOrder.query.filter_by(
        id=order_id,
        owner_id=current_user.id
    ).first_or_404()

    # Seules les commandes en attente peuvent être modifiées
    if order.status != PurchaseOrderStatus.PENDING:
        flash('Seules les commandes en attente peuvent être modifiées', 'error')
        return redirect(url_for('inventory.purchase_order_details', order_id=order_id))

    if request.method == 'GET':
        # Afficher le formulaire de modification
        suppliers = Supplier.query.filter_by(
            owner_id=current_user.id,
            is_active=True
        ).all()

        products = Product.query.filter_by(
            owner_id=current_user.id,
            is_active=True
        ).all()

        from app.modules.inventory.models_ingredient import Ingredient
        ingredients = Ingredient.query.filter_by(
            owner_id=current_user.id
        ).all()

        # Convertir les objets en dictionnaires pour la sérialisation JSON
        products_data = [
            {
                'id': p.id,
                'name': p.name,
                'price': p.price,
                'unit': p.unit,
                'stock_quantity': p.stock_quantity
            } for p in products
        ]

        ingredients_data = [
            {
                'id': i.id,
                'name': i.name,
                'price_per_unit': i.price_per_unit,
                'unit': i.unit,
                'stock_quantity': i.stock_quantity
            } for i in ingredients
        ]

        return render_template('inventory/stock_replenishment/edit_order.html',
                             order=order,
                             suppliers=suppliers,
                             products=products_data,
                             ingredients=ingredients_data)

    elif request.method == 'POST':
        # Traiter la modification
        try:
            data = request.get_json()

            # Mettre à jour les informations de base
            order.supplier_id = data.get('supplier_id') if data.get('supplier_id') != 0 else None
            order.notes = data.get('notes', '')
            order.expected_delivery_date = datetime.strptime(data['expected_delivery_date'], '%Y-%m-%d') if data.get('expected_delivery_date') else None

            # Supprimer les anciens articles
            for item in order.items:
                db.session.delete(item)

            # Ajouter les nouveaux articles
            subtotal = 0
            for item_data in data.get('items', []):
                item = PurchaseOrderItem(
                    purchase_order_id=order.id,
                    product_id=item_data.get('product_id') if item_data.get('product_id') != 0 else None,
                    ingredient_id=item_data.get('ingredient_id') if item_data.get('ingredient_id') != 0 else None,
                    item_name=item_data['item_name'],
                    quantity=item_data['quantity'],
                    unit_price=item_data['unit_price'],
                    total_price=item_data['quantity'] * item_data['unit_price'],
                    notes=item_data.get('notes', '')
                )
                db.session.add(item)
                subtotal += item.total_price

            # Mettre à jour les totaux
            order.subtotal = subtotal
            discount_info = data.get('discount', {})
            order.discount_amount = discount_info.get('amount', 0)
            order.discount_type = discount_info.get('type', 'amount')
            order.total_amount = data.get('total_amount', subtotal)
            order.updated_at = datetime.utcnow()

            db.session.commit()

            return jsonify({'success': True, 'message': 'Commande modifiée avec succès'})

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Erreur lors de la modification: {str(e)}")
            return jsonify({'success': False, 'error': 'Erreur lors de la modification'}), 500

@bp.route('/stock-replenishment/orders/<int:order_id>/pay', methods=['POST'])
@login_required
@permission_required
def pay_order(order_id):
    """Marquer une commande comme payée"""
    try:
        order = PurchaseOrder.query.filter_by(
            id=order_id,
            owner_id=current_user.id
        ).first_or_404()

        if order.status != PurchaseOrderStatus.RECEIVED_UNPAID:
            return jsonify({'success': False, 'error': 'Cette commande ne peut pas être payée dans son état actuel'})

        success = order.mark_as_paid()

        if success:
            db.session.commit()
            return jsonify({'success': True, 'message': 'Commande marquée comme payée'})
        else:
            return jsonify({'success': False, 'error': 'Impossible de marquer cette commande comme payée'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors du paiement: {str(e)}")
        return jsonify({'success': False, 'error': 'Erreur lors du paiement'}), 500

@bp.route('/api/pending-orders/status-check')
@login_required
@permission_required
def check_pending_orders_status():
    """API pour vérifier s'il y a des mises à jour de statut des commandes en attente"""
    try:
        # Compter les commandes en attente
        current_pending_count = PurchaseOrder.query.filter(
            PurchaseOrder.owner_id == current_user.id,
            PurchaseOrder.status.in_([PurchaseOrderStatus.PENDING, PurchaseOrderStatus.PARTIAL_RECEIVED])
        ).count()

        # Pour cette implémentation simple, on retourne toujours False
        # Dans une version plus avancée, on pourrait stocker le dernier count en session
        # et comparer avec le count actuel
        return jsonify({
            'updates_available': False,
            'current_count': current_pending_count
        })

    except Exception as e:
        current_app.logger.error(f"Erreur lors de la vérification du statut: {str(e)}")
        return jsonify({'updates_available': False, 'current_count': 0})

# API Routes pour les informations de stock et ajustements

@bp.route('/api/stock-info/<item_type>/<int:item_id>')
@login_required
@permission_required
def get_stock_info(item_type, item_id):
    """API pour récupérer les informations de stock d'un article"""
    try:
        if item_type == 'product':
            item = Product.query.filter_by(
                id=item_id,
                owner_id=current_user.id
            ).first_or_404()

            return jsonify({
                'success': True,
                'name': item.name,
                'stock_quantity': item.stock_quantity,
                'minimum_stock': item.minimum_stock,
                'unit': item.unit,
                'price': item.price
            })

        elif item_type == 'ingredient':
            item = Ingredient.query.filter_by(
                id=item_id,
                owner_id=current_user.id
            ).first_or_404()

            return jsonify({
                'success': True,
                'name': item.name,
                'stock_quantity': item.stock_quantity,
                'minimum_stock': item.minimum_stock,
                'unit': item.unit,
                'price': item.price_per_unit
            })
        else:
            return jsonify({'success': False, 'error': 'Type d\'article invalide'})

    except Exception as e:
        current_app.logger.error(f"Erreur lors de la récupération des infos de stock: {str(e)}")
        return jsonify({'success': False, 'error': 'Erreur lors de la récupération'}), 500

@bp.route('/api/recent-stock-adjustments')
@login_required
@permission_required
def get_recent_stock_adjustments():
    """API pour récupérer les ajustements de stock récents"""
    try:
        # Cette route retourne des ajustements fictifs pour l'instant
        # Dans une vraie implémentation, on récupérerait les vrais ajustements depuis la base
        adjustments = [
            {
                'item_name': 'Exemple Produit',
                'quantity': 10,
                'reason': 'Réception marchandise',
                'date': '12/08/2025'
            }
        ]

        return jsonify({
            'success': True,
            'adjustments': adjustments
        })

    except Exception as e:
        current_app.logger.error(f"Erreur lors de la récupération des ajustements: {str(e)}")
        return jsonify({'success': False, 'error': 'Erreur lors de la récupération'}), 500


@bp.route('/api/item-details/<item_type>/<int:item_id>')
@login_required
@permission_required
def get_item_details(item_type, item_id):
    """API pour récupérer les détails d'un article (produit ou ingrédient)"""
    try:
        if item_type == 'product':
            item = Product.query.filter_by(
                id=item_id,
                owner_id=current_user.id
            ).first_or_404()

            # Récupérer le dernier prix d'achat depuis les commandes
            last_price = None
            last_order_item = PurchaseOrderItem.query.join(PurchaseOrder).filter(
                PurchaseOrder.owner_id == current_user.id,
                PurchaseOrderItem.product_id == item_id
            ).order_by(PurchaseOrderItem.id.desc()).first()

            if last_order_item:
                last_price = last_order_item.unit_price

            # Récupérer le fournisseur habituel
            usual_supplier = None
            if item.supplier_id:
                supplier = Supplier.query.filter_by(
                    id=item.supplier_id,
                    owner_id=current_user.id
                ).first()
                if supplier:
                    usual_supplier = supplier.name

            return jsonify({
                'type': 'product',
                'id': item.id,
                'name': item.name,
                'stock_quantity': item.stock_quantity or 0,
                'minimum_stock': item.minimum_stock or 0,
                'unit': item.unit or 'unité',
                'last_price': last_price,
                'usual_supplier': usual_supplier
            })

        elif item_type == 'ingredient':
            item = Ingredient.query.filter_by(
                id=item_id,
                owner_id=current_user.id
            ).first_or_404()

            # Récupérer le dernier prix d'achat depuis les commandes
            last_price = None
            last_order_item = PurchaseOrderItem.query.join(PurchaseOrder).filter(
                PurchaseOrder.owner_id == current_user.id,
                PurchaseOrderItem.ingredient_id == item_id
            ).order_by(PurchaseOrderItem.id.desc()).first()

            if last_order_item:
                last_price = last_order_item.unit_price

            # Récupérer le fournisseur habituel
            usual_supplier = None
            if item.supplier_id:
                supplier = Supplier.query.filter_by(
                    id=item.supplier_id,
                    owner_id=current_user.id
                ).first()
                if supplier:
                    usual_supplier = supplier.name

            return jsonify({
                'type': 'ingredient',
                'id': item.id,
                'name': item.name,
                'stock_quantity': item.stock_quantity or 0,
                'minimum_stock': item.minimum_stock or 0,
                'unit': item.unit or 'unité',
                'last_price': last_price,
                'usual_supplier': usual_supplier
            })
        else:
            return jsonify({'success': False, 'error': 'Type d\'article invalide'}), 400

    except Exception as e:
        current_app.logger.error(f"Erreur lors de la récupération des détails de l'article: {str(e)}")
        return jsonify({'success': False, 'error': 'Erreur lors de la récupération'}), 500

@bp.route('/debug/invoices')
@login_required
@permission_required
def debug_invoices():
    """Route de débogage pour vérifier les factures"""
    invoices = SupplierInvoice.query.filter_by(owner_id=current_user.id).all()

    debug_info = []
    for invoice in invoices:
        debug_info.append({
            'id': invoice.id,
            'reference': invoice.reference,
            'supplier_id': invoice.supplier_id,
            'supplier_name': invoice.supplier.name if invoice.supplier else 'N/A',
            'purchase_order_id': invoice.purchase_order_id,
            'amount': invoice.amount,
            'payment_status': invoice.payment_status.value,
            'created_at': invoice.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    return jsonify({
        'total_invoices': len(invoices),
        'invoices': debug_info
    })
